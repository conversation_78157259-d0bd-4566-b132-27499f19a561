name: CI
on:
  - push
  - pull_request
jobs:
  test: # from https://github.com/simeonschaub/OptionalArgChecks.jl/blob/457488ef04ed68a37aff10af1cb3909f47a8230c/.github/workflows/ci.yml#L23-L37
    name: Julia ${{ matrix.version }} - ${{ matrix.os }} - ${{ matrix.arch }}
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        version:
          - '1.10'
          - '1'
        os:
          - ubuntu-latest
          - macOS-latest
          - windows-latest
        arch:
          - x64
      fail-fast: false
    steps:
      - uses: actions/checkout@v2
      - uses: julia-actions/setup-julia@v1
        with:
          version: ${{ matrix.version }}
          arch: ${{ matrix.arch }}
      - uses: julia-actions/julia-buildpkg@latest
        continue-on-error: ${{ matrix.version == 'nightly' }}
      - uses: julia-actions/julia-runtest@latest
        continue-on-error: ${{ matrix.version == 'nightly' }}
      - uses: julia-actions/julia-uploadcodecov@latest
        env:
          CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
      
  docs:
    name: Documentation
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: julia-actions/setup-julia@v1
        with:
          version: '1'
      - run: |
          julia --project=docs -e '
            using Pkg
            Pkg.develop(PackageSpec(path=pwd()))
            Pkg.instantiate()
            Pkg.add("Documenter")'
      - run: julia --project=docs docs/make.jl
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          # Needed due to https://github.com/JuliaDocs/Documenter.jl/issues/1177
          DOCUMENTER_KEY: ${{ secrets.DOCUMENTER_KEY }}
