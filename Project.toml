name = "FinanceModels"
uuid = "77f2ae65-bdde-421f-ae9d-22f1af19dd76"
authors = ["<PERSON> <alec<PERSON><EMAIL>> and contributors"]
version = "4.15.0"

[deps]
AccessibleOptimization = "d88a00a0-4a21-4fe4-a515-e2123c37b885"
Accessors = "7d9f7c33-5ae7-4f3b-8dc6-eff91059b697"
DataInterpolations = "82cc6244-b520-54b8-b5a6-8a565e85f1d0"
Dates = "ade2ca70-3891-5945-98fb-dc099432e06a"
DifferentiationInterface = "a0c0ee7d-e4b9-4e03-894e-1c5f64a51d63"
FinanceCore = "b9b1ffdd-6612-4b69-8227-7663be06e089"
IntervalSets = "8197267c-284f-5f27-9208-e0e47529a953"
LinearAlgebra = "37e2e46d-f89d-539d-b4ee-838fcccc9c8e"
Optimization = "7f7a1694-90dd-40f0-9382-eb1efda571ba"
OptimizationMetaheuristics = "3aafef2f-86ae-4776-b337-85a36adf0b55"
OptimizationOptimJL = "36348300-93cb-4f02-beb5-3c3902f8871e"
PrecompileTools = "aea7be01-6a6a-4083-8856-8a6e6704d82a"
Reexport = "189a3867-3050-52da-a836-e630ba90ab69"
SpecialFunctions = "276daf66-3868-5448-9aa4-cd146d93841b"
StaticArrays = "90137ffa-7385-5640-81b9-e52037218182"
Transducers = "28d57a85-8fef-5791-bfe6-a80928e7c999"

[weakdeps]
MakieCore = "20f20a25-4f0e-4fdf-b5d1-57303727442b"
UnicodePlots = "b8865327-cd53-5732-bb35-84acbb429228"

[extensions]
FinanceModelsMakieCoreExt = "MakieCore"
FinanceModelsUnicodePlots = "UnicodePlots"

[compat]
AccessibleOptimization = "^0.1.2"
Accessors = "^0.1"
DataInterpolations = "8"
Dates = "^1.6"
DifferentiationInterface = "0.7.4"
FinanceCore = "^2.1"
IntervalSets = "^0.7"
LinearAlgebra = "1"
MakieCore = "0.8,0.9"
Optimization = "4"
OptimizationMetaheuristics = "0.3"
OptimizationOptimJL = "0.4.3"
PrecompileTools = "^1.1"
Reexport = "^1.2"
SpecialFunctions = "2"
StaticArrays = "^1.6"
Transducers = "^0.4"
UnicodePlots = "^3.6"
julia = "1.10"
