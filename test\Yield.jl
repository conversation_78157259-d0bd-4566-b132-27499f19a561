@testset "constant curve and rate -> Constant" begin
    yield = Yield.Constant(0.05)

    @test zero(yield, 1) ≈ Periodic(0.05, 1)
    @test zero(Yield.Constant(Periodic(0.05, 2)), 10) ≈ Periodic(0.05, 2)

    @testset "constant discount time: $time" for time in [0, 0.5, 1, 10]
        @test discount(yield, time) ≈ 1 / (1.05)^time
        @test discount(yield, 0, time) ≈ 1 / (1.05)^time
        @test discount(yield, 3, time + 3) ≈ 1 / (1.05)^time
    end
    @testset "constant discount scalar time: $time" for time in [0, 0.5, 1, 10]
        @test discount(0.05, time) ≈ 1 / (1.05)^time
    end

    @testset "constant accumulation scalar time: $time" for time in [0, 0.5, 1, 10]
        @test accumulation(0.05, time) ≈ 1 * (1.05)^time
    end

    @testset "broadcasting" begin
        @test all(discount.(yield, [1, 2, 3]) .≈ 1 ./ 1.05 .^ (1:3))
        @test all(accumulation.(yield, [1, 2, 3]) .≈ 1.05 .^ (1:3))
    end

    @testset "constant accumulation time: $time" for time in [0, 0.5, 1, 10]
        @test accumulation(yield, time) ≈ 1 * 1.05^time
        @test accumulation(yield, 0, time) ≈ 1 * 1.05^time
        @test accumulation(yield, 3, time + 3) ≈ 1 * 1.05^time
    end


    yield_2x = yield + yield
    yield_add = yield + 0.05
    add_yield = 0.05 + yield
    @testset "constant discount added" for time in [0, 0.5, 1, 10]
        @test discount(yield_2x, time) ≈ 1 / (1.1)^time
        @test discount(yield_add, time) ≈ 1 / (1.1)^time
        @test discount(add_yield, time) ≈ 1 / (1.1)^time
    end

    yield_1bps = yield - Yield.Constant(0.04)
    yield_minus = yield - 0.01
    minus_yield = 0.05 - Yield.Constant(0.01)
    @testset "constant discount subtraction" for time in [0, 0.5, 1, 10]
        @test discount(yield_1bps, time) ≈ 1 / (1.01)^time
        @test discount(yield_minus, time) ≈ 1 / (1.04)^time
        @test discount(minus_yield, time) ≈ 1 / (1.04)^time
    end
end

@testset "bootstrapped curves" begin


    @testset "short curve" begin
        zs = ZCBYield.([0.0, 0.05], [1, 2])
        z = fit(Spline.Cubic(), zs, Fit.Bootstrap())

        @test isapprox(zero(z, 1), Periodic(0.0, 1); atol = 1.0e-12)
        @test discount(z, 1) ≈ 1.0
        @test zero(z, 2) ≈ Periodic(0.05, 1)

        # test no times constructor
        zs = ZCBYield([0.0, 0.05])
        z = fit(Spline.Cubic(), zs, Fit.Bootstrap())
        @test zero(z, 1) ≈ Periodic(0.0, 1)
        @test discount(z, 1) ≈ 1.0
        @test zero(z, 2) ≈ Periodic(0.05, 1)
    end

    @testset "Salomon Understanding the Yield Curve Pt 1 Figure 9" begin
        maturity = collect(1:10)

        par = [6.0, 8.0, 9.5, 10.5, 11.0, 11.25, 11.38, 11.44, 11.48, 11.5] ./ 100
        spot = [6.0, 8.08, 9.72, 10.86, 11.44, 11.71, 11.83, 11.88, 11.89, 11.89] ./ 100

        # the forwards for 7+ have been adjusted from the table - perhaps rounding issues are exacerbated
        # in the text? forwards for <= 6 matched so reasonably confident that the algo is correct
        # fwd = [6.,10.2,13.07,14.36,13.77,13.1,12.55,12.2,11.97,11.93] ./ 100 # from text
        fwd = [6.0, 10.2, 13.07, 14.36, 13.77, 13.1, 12.61, 12.14, 12.05, 11.84] ./ 100  # modified

        rs = FinanceModels.ParYield.(Periodic(1).(par), maturity)
        m = fit(Spline.Cubic(), rs, Fit.Bootstrap())
        @testset "quadratic UTYC Figure 9 par -> spot : $mat" for mat in maturity
            @test zero(m, mat) ≈ Periodic(spot[mat], 1) atol = 0.0001
            @test forward(m, mat - 1) ≈ FinanceModels.Periodic(fwd[mat], 1) atol = 0.0001
        end

        m = fit(Spline.Linear(), rs, Fit.Bootstrap())

        @testset "linear UTYC Figure 9 par -> spot : $mat" for mat in maturity
            @test zero(m, mat) ≈ Periodic(spot[mat], 1) atol = 0.0001
            @test forward(m, mat - 1) ≈ FinanceModels.Periodic(fwd[mat], 1) atol = 0.0001
        end

    end

    @testset "Hull" begin
        # Par Yield, pg 85

        c = ParYield.(FinanceModels.Periodic.([0.0687, 0.0687], 2), [2, 3])

        m = fit(Spline.Linear(), c, Fit.Bootstrap())
        @test FinanceModels.par(m, 2) ≈ FinanceModels.Periodic(0.0687, 2) atol = 0.00001

    end

    @testset "simple rate and forward" begin
        # Risk Managment and Financial Institutions, 5th ed. Appendix B

        maturity = [0.5, 1.0, 1.5, 2.0]
        zero = [5.0, 5.8, 6.4, 6.8] ./ 100
        zs = ZCBYield.(zero, maturity)
        variants = [
            Spline.Cubic(),
            Spline.Linear(),
            Spline.Quadratic(),
            Spline.BSpline(5),
            Spline.BSpline(3),
            Spline.BSpline(1),
            Spline.PolynomialSpline(1),
            Spline.PolynomialSpline(2),
            Spline.PolynomialSpline(3),
        ]
        @testset "Constructor $i" for (i, m) in enumerate(variants)
            curve = fit(m, zs, Fit.Bootstrap())
            @test discount(curve, 1) ≈ 1 / 1.058
            @test discount(curve, 1.5) ≈ 1 / 1.064^1.5
            @test discount(curve, 2) ≈ 1 / 1.068^2

            @test discount(curve, 0) ≈ 1
            @test discount(curve, 1) ≈ 1 / (1 + zero[2])
            @test discount(curve, 2) ≈ 1 / (1 + zero[4])^2

            @test forward(curve, 0.5, 1.0) ≈ FinanceModels.Periodic(6.6 / 100, 1) atol = 0.001
            @test forward(curve, 1.0, 1.5) ≈ FinanceModels.Periodic(7.6 / 100, 1) atol = 0.001
            @test forward(curve, 1.5, 2.0) ≈ FinanceModels.Periodic(8.0 / 100, 1) atol = 0.001
            @testset "broadcasting" begin
                @test all(discount.(curve, [1, 2]) .≈ [1 / 1.058, 1 / 1.068^2])
                @test all(accumulation.(curve, [1, 2]) .≈ [1.058, 1.068^2])
            end
        end

    end

    @testset "Forward Rates" begin
        # Risk Managment and Financial Institutions, 5th ed. Appendix B
        forwards = [0.05, 0.04, 0.03, 0.08]
        qs = ForwardYields(forwards, [1, 2, 3, 4])
        curve = fit(Spline.Cubic(), qs, Fit.Bootstrap())


        @testset "discounts: $t" for (t, r) in enumerate(forwards)
            @test discount(curve, t) ≈ reduce((v, r) -> v / (1 + r), forwards[1:t]; init = 1.0)
        end

        # test from ActuaryUtilities

        @testset "pv with vector discount rates" begin
            cf = [100, 100]
            f(rates) = fit(Spline.Linear(), ForwardYields(rates), Fit.Bootstrap())
            f(rates, times) = fit(Spline.Linear(), ForwardYields(rates, times), Fit.Bootstrap())
            @test pv(f([0.0, 0.05]), cf) ≈ 100 / 1.0 + 100 / 1.05
            @test pv(f([0.0, 0.05]), cf) ≈ 100 / 1.0 + 100 / 1.05
            @test pv(f([0.05, 0.0]), cf) ≈ 100 / 1.05 + 100 / 1.05
            @test pv(f([0.05, 0.1]), cf) ≈ 100 / 1.05 + 100 / 1.05 / 1.1

            ts = [0.5, 1]
            @test pv(f([0.0, 0.05], ts), cf, ts) ≈ 100 / 1.0 + 100 / 1.05^0.5
            @test pv(f([0.05, 0.0], ts), cf, ts) ≈ 100 / 1.05^0.5 + 100 / 1.05^0.5
            @test pv(f([0.05, 0.1], ts), cf, ts) ≈ 100 / 1.05^0.5 + 100 / (1.05^0.5) / (1.1^0.5)

        end


        # test constructor without times
        qs = ForwardYields(forwards)

        @testset "discounts: $t" for (t, r) in enumerate(forwards)
            @test discount(curve, t) ≈ reduce((v, r) -> v / (1 + r), forwards[1:t]; init = 1.0)
        end

        @test accumulation(curve, 0, 1) ≈ 1.05
        @test accumulation(curve, 1, 2) ≈ 1.04
        @test accumulation(curve, 0, 2) ≈ 1.04 * 1.05

        @testset "broadcasting" begin
            @test all(accumulation.(curve, [1, 2]) .≈ [1.05, 1.04 * 1.05])
            @test all(discount.(curve, [1, 2]) .≈ 1 ./ [1.05, 1.04 * 1.05])
        end

        # test construction using vector of reals and of Rates
        curve_c = let
            qs = ForwardYields(Continuous.(forwards), [1, 2, 3, 4])
            fit(Spline.Cubic(), qs, Fit.Bootstrap())
        end
        @test discount(curve, 1) > discount(curve_c, 1)


        # addition / subtraction
        @test discount(curve + 0.1, 1) ≈ 1 / 1.15
        @test discount(curve - 0.03, 1) ≈ 1 / 1.02


        @testset "with specified non integer timepoints" begin
            i = [0.0, 0.05]
            times = [0.5, 1.5]
            qs = ForwardYields(i, times)
            m = fit(Spline.Linear(), qs, Fit.Bootstrap())
            @test discount(m, 0.5) ≈ 1 / 1.0^0.5
            @test discount(m, 1.5) ≈ 1 / 1.0^0.5 / 1.05^1

        end

    end

    @testset "forwardcurve" begin
        maturity = [0.5, 1.0, 1.5, 2.0]
        zeros = [5.0, 5.8, 6.4, 6.8] ./ 100
        qs = ZCBYield.(zeros, maturity)
        curve = fit(Spline.Cubic(), qs, Fit.Bootstrap())

        fwd = Yield.ForwardStarting(curve, 1.0)
        @test discount(fwd, 0) ≈ 1
        @test discount(fwd, 0.5) ≈ discount(curve, 1, 1.5)
        @test discount(fwd, 1) ≈ discount(curve, 1, 2)
        @test accumulation(fwd, 1) ≈ accumulation(curve, 1, 2)

        @test zero(fwd, 1) ≈ forward(curve, 1, 2)
    end


    @testset "actual cmt treasury" begin
        # Fabozzi 5-5,5-6
        cmt = [5.25, 5.5, 5.75, 6.0, 6.25, 6.5, 6.75, 6.8, 7.0, 7.1, 7.15, 7.2, 7.3, 7.35, 7.4, 7.5, 7.6, 7.6, 7.7, 7.8] ./ 100
        mats = collect(0.5:0.5:10.0)
        qs = CMTYield.(cmt, mats)
        target_raw = [5.25, 5.5, 5.76, 6.02, 6.28, 6.55, 6.82, 6.87, 7.09, 7.2, 7.26, 7.31, 7.43, 7.48, 7.54, 7.67, 7.8, 7.79, 7.93, 8.07] ./ 100
        targets = Periodic(2).(target_raw)
        targets[1:2] .= Periodic(1)(target_raw[1:2]) # 1 year is a no-coupon, BEY yield, the rest are semiannual BEY

        curves = [
            fit(Spline.Linear(), qs, Fit.Bootstrap()),
            fit(Spline.Quadratic(), qs, Fit.Bootstrap()),
            fit(Spline.Cubic(), qs, Fit.Bootstrap()),
        ]

        @testset "curve bootstrapping choices" for curve in curves
            @testset "Fabozzi bootstrapped rates" for (r, mat, target) in zip(cmt, mats, targets)
                @test zero(curve, mat) ≈ target atol = 0.0001
            end
        end

        # Hull, problem 4.34
        adj = ((1 + 0.051813 / 2)^2 - 1) * 100
        cmt = [4.0816, adj, 5.4986, 5.862] ./ 100
        mats = [0.5, 1.0, 1.5, 2.0]
        curve = fit(Spline.Linear(), CMTYield.(cmt, mats), Fit.Bootstrap())
        targets = Continuous.([4.0405, 5.1293, 5.4429, 5.8085] ./ 100)
        @testset "Hull bootstrapped rates" for (r, mat, target) in zip(cmt, mats, targets)
            @test zero(curve, mat) ≈ target atol = 0.001
        end

        # test that showing the curve doesn't error
        @test length(repr(curve)) > 0

        #     # https://www.federalreserve.gov/pubs/feds/2006/200628/200628abs.html
        #     # 2020-04-02 data
        #     cmt = [0.0945,0.2053,0.4431,0.7139,0.9724,1.2002,1.3925,1.5512,1.6805,1.7853,1.8704,1.9399,1.9972,2.045,2.0855,2.1203,2.1509,2.1783,2.2031,2.2261,2.2477,2.2683,2.2881,2.3074,2.3262,2.3447,2.3629,2.3809,2.3987,2.4164] ./ 100
        #     mats = collect(1:30)
        #     curve = FinanceModels.USCMT(cmt,mats)
        #     target = [0.0945,0.2053,0.444,0.7172,0.9802,1.2142,1.4137,1.5797,1.7161,1.8275,1.9183,1.9928,2.0543,2.1056,2.1492,2.1868,2.2198,2.2495,2.2767,2.302,2.3261,2.3494,2.372,2.3944,2.4167,2.439,2.4614,2.4839,2.5067,2.5297] ./ 100

        #     @testset "FRB data" for (t,mat,target) in zip(1:length(mats),mats,target)
        #         @show mat
        #         if mat >= 1
        #             @test rate(zero(curve,mat, FinanceModels.Continuous())) ≈ target[mat] atol=0.001
        #         end
        #     end
    end

    @testset "OIS" begin
        ois = [1.8, 2.0, 2.2, 2.5, 3.0, 4.0] ./ 100
        mats = [1 / 12, 1 / 4, 1 / 2, 1, 2, 5]
        qs = OISYield.(ois, mats)
        targets = Continuous.([0.017987, 0.01995, 0.02188, 0.024693, 0.029994, 0.040401])

        curve = fit(Spline.Linear(), qs, Fit.Bootstrap())
        @testset "bootstrapped rates" for (mat, target) in zip(mats, targets)
            @test zero(curve, mat) ≈ target atol = 0.001
        end
        curve = fit(Spline.Cubic(), qs, Fit.Bootstrap())
        @testset "bootstrapped rates" for (mat, target) in zip(mats, targets)
            @test zero(curve, mat) ≈ target atol = 0.001
        end
    end

    @testset "par" begin
        @testset "first payment logic" begin
            ct = Bond.coupon_times
            @test ct(0.5, 1) ≈ 0.5:1:0.5
            @test ct(1.5, 1) ≈ 0.5:1:1.5
            @test ct(0.75, 1) ≈ 0.75:1:0.75
            @test ct(1, 1) ≈ 1:1:1
            @test ct(1, 2) ≈ 0.5:0.5:1.0
            @test ct(0.5, 2) ≈ 0.5:0.5:0.5
            @test ct(1.5, 2) ≈ 0.5:0.5:1.5
        end

        # https://quant.stackexchange.com/questions/57608/how-to-compute-par-yield-from-zero-rate-curve
        c = fit(Spline.Cubic(), ZCBYield.(Continuous.([0.02, 0.025, 0.03, 0.035]), 0.5:0.5:2), Fit.Bootstrap())
        @test FinanceModels.par(c, 2) ≈ Periodic(0.03508591, 2) atol = 0.000001

        c = Yield.Constant(0.04)
        @testset "misc combinations" for t in 0.5:0.5:5
            @test FinanceModels.par(c, t; frequency = 1) ≈ FinanceModels.Periodic(0.04, 1)
            @test FinanceModels.par(c, t) ≈ FinanceModels.Periodic(0.04, 1)
            @test FinanceModels.par(c, t, frequency = 4) ≈ FinanceModels.Periodic(0.04, 1)
        end

        @test FinanceModels.par(c, 0.6) ≈ FinanceModels.Periodic(0.04, 1)

        @testset "round trip" begin
            maturity = collect(1:10)

            pars = [6.0, 8.0, 9.5, 10.5, 11.0, 11.25, 11.38, 11.44, 11.48, 11.5] ./ 100

            curve = fit(Spline.Cubic(), ParYield.(pars, maturity), Fit.Bootstrap())

            for (p, m) in zip(pars, maturity)
                @test par(curve, m) ≈ Periodic(p, 2) atol = 0.001
            end
        end


    end

end


@testset "spline fit curves" begin


    @testset "short curve" begin
        zs = ZCBYield.([0.0, 0.05], [1, 2])
        z = fit(Spline.Cubic(), zs)

        @test isapprox(zero(z, 1), Periodic(0.0, 1); atol = 1.0e-12)
        @test discount(z, 1) ≈ 1.0
        @test zero(z, 2) ≈ Periodic(0.05, 1)

        # test no times constructor
        zs = ZCBYield([0.0, 0.05])
        z = fit(Spline.Cubic(), zs)
        @test isapprox(zero(z, 1), Periodic(0.0, 1); atol = 1.0e-12)
        @test discount(z, 1) ≈ 1.0
        @test zero(z, 2) ≈ Periodic(0.05, 1)
    end

    @testset "Salomon Understanding the Yield Curve Pt 1 Figure 9" begin
        maturity = collect(1:10)

        par = [6.0, 8.0, 9.5, 10.5, 11.0, 11.25, 11.38, 11.44, 11.48, 11.5] ./ 100
        spot = [6.0, 8.08, 9.72, 10.86, 11.44, 11.71, 11.83, 11.88, 11.89, 11.89] ./ 100

        # the forwards for 7+ have been adjusted from the table - perhaps rounding issues are exacerbated
        # in the text? forwards for <= 6 matched so reasonably confident that the algo is correct
        # fwd = [6.,10.2,13.07,14.36,13.77,13.1,12.55,12.2,11.97,11.93] ./ 100 # from text
        fwd = [6.0, 10.2, 13.07, 14.36, 13.77, 13.1, 12.61, 12.14, 12.05, 11.84] ./ 100  # modified

        rs = FinanceModels.ParYield.(Periodic(1).(par), maturity)
        m = fit(Spline.Cubic(), rs)
        @testset "quadratic UTYC Figure 9 par -> spot : $mat" for mat in maturity
            @test zero(m, mat) ≈ Periodic(spot[mat], 1) atol = 0.0001
            @test forward(m, mat - 1) ≈ FinanceModels.Periodic(fwd[mat], 1) atol = 0.0001
        end

        m = fit(Spline.Linear(), rs)

        @testset "linear UTYC Figure 9 par -> spot : $mat" for mat in maturity
            @test zero(m, mat) ≈ Periodic(spot[mat], 1) atol = 0.0001
            @test forward(m, mat - 1) ≈ FinanceModels.Periodic(fwd[mat], 1) atol = 0.0001
        end

    end

    @testset "Hull" begin
        # Par Yield, pg 85

        c = ParYield.(FinanceModels.Periodic.([0.0687, 0.0687], 2), [2, 3])

        m = fit(Spline.Linear(), c)
        @test FinanceModels.par(m, 2) ≈ FinanceModels.Periodic(0.0687, 2) atol = 0.00001

    end

    @testset "simple rate and forward" begin
        # Risk Managment and Financial Institutions, 5th ed. Appendix B

        maturity = [0.5, 1.0, 1.5, 2.0]
        zero = [5.0, 5.8, 6.4, 6.8] ./ 100
        zs = ZCBYield.(zero, maturity)
        variants = [
            Spline.Cubic(),
            Spline.Linear(),
            Spline.Quadratic(),
            Spline.BSpline(5),
            Spline.BSpline(3),
            Spline.BSpline(1),
            Spline.PolynomialSpline(1),
            Spline.PolynomialSpline(2),
            Spline.PolynomialSpline(3),
        ]
        @testset "Constructor $i" for (i, m) in enumerate(variants)
            curve = fit(m, zs)
            @test discount(curve, 1) ≈ 1 / 1.058
            @test discount(curve, 1.5) ≈ 1 / 1.064^1.5
            @test discount(curve, 2) ≈ 1 / 1.068^2

            @test discount(curve, 0) ≈ 1
            @test discount(curve, 1) ≈ 1 / (1 + zero[2])
            @test discount(curve, 2) ≈ 1 / (1 + zero[4])^2

            @test forward(curve, 0.5, 1.0) ≈ FinanceModels.Periodic(6.6 / 100, 1) atol = 0.001
            @test forward(curve, 1.0, 1.5) ≈ FinanceModels.Periodic(7.6 / 100, 1) atol = 0.001
            @test forward(curve, 1.5, 2.0) ≈ FinanceModels.Periodic(8.0 / 100, 1) atol = 0.001
            @testset "broadcasting" begin
                @test all(discount.(curve, [1, 2]) .≈ [1 / 1.058, 1 / 1.068^2])
                @test all(accumulation.(curve, [1, 2]) .≈ [1.058, 1.068^2])
            end
        end

    end

    @testset "Forward Rates" begin
        # Risk Managment and Financial Institutions, 5th ed. Appendix B
        forwards = [0.05, 0.04, 0.03, 0.08]
        qs = ForwardYields(forwards, [1, 2, 3, 4])
        curve = fit(Spline.Cubic(), qs)


        @testset "discounts: $t" for (t, r) in enumerate(forwards)
            @test discount(curve, t) ≈ reduce((v, r) -> v / (1 + r), forwards[1:t]; init = 1.0)
        end

        # test from ActuaryUtilities

        @testset "pv with vector discount rates" begin
            cf = [100, 100]
            f(rates) = fit(Spline.Linear(), ForwardYields(rates))
            f(rates, times) = fit(Spline.Linear(), ForwardYields(rates, times))
            @test pv(f([0.0, 0.05]), cf) ≈ 100 / 1.0 + 100 / 1.05
            @test pv(f([0.0, 0.05]), cf) ≈ 100 / 1.0 + 100 / 1.05
            @test pv(f([0.05, 0.0]), cf) ≈ 100 / 1.05 + 100 / 1.05
            @test pv(f([0.05, 0.1]), cf) ≈ 100 / 1.05 + 100 / 1.05 / 1.1

            ts = [0.5, 1]
            @test pv(f([0.0, 0.05], ts), cf, ts) ≈ 100 / 1.0 + 100 / 1.05^0.5
            @test pv(f([0.05, 0.0], ts), cf, ts) ≈ 100 / 1.05^0.5 + 100 / 1.05^0.5
            @test pv(f([0.05, 0.1], ts), cf, ts) ≈ 100 / 1.05^0.5 + 100 / (1.05^0.5) / (1.1^0.5)

        end


        # test constructor without times
        qs = ForwardYields(forwards)

        @testset "discounts: $t" for (t, r) in enumerate(forwards)
            @test discount(curve, t) ≈ reduce((v, r) -> v / (1 + r), forwards[1:t]; init = 1.0)
        end

        @test accumulation(curve, 0, 1) ≈ 1.05
        @test accumulation(curve, 1, 2) ≈ 1.04
        @test accumulation(curve, 0, 2) ≈ 1.04 * 1.05

        @testset "broadcasting" begin
            @test all(accumulation.(curve, [1, 2]) .≈ [1.05, 1.04 * 1.05])
            @test all(discount.(curve, [1, 2]) .≈ 1 ./ [1.05, 1.04 * 1.05])
        end

        # test construction using vector of reals and of Rates
        curve_c = let
            qs = ForwardYields(Continuous.(forwards), [1, 2, 3, 4])
            fit(Spline.Cubic(), qs)
        end
        @test discount(curve, 1) > discount(curve_c, 1)


        # addition / subtraction
        @test discount(curve + 0.1, 1) ≈ 1 / 1.15
        @test discount(curve - 0.03, 1) ≈ 1 / 1.02


        @testset "with specified non integer timepoints" begin
            i = [0.0, 0.05]
            times = [0.5, 1.5]
            qs = ForwardYields(i, times)
            m = fit(Spline.Linear(), qs)
            @test discount(m, 0.5) ≈ 1 / 1.0^0.5
            @test discount(m, 1.5) ≈ 1 / 1.0^0.5 / 1.05^1

        end

    end

    @testset "forwardcurve" begin
        maturity = [0.5, 1.0, 1.5, 2.0]
        zeros = [5.0, 5.8, 6.4, 6.8] ./ 100
        qs = ZCBYield.(zeros, maturity)
        curve = fit(Spline.Cubic(), qs)

        fwd = Yield.ForwardStarting(curve, 1.0)
        @test discount(fwd, 0) ≈ 1
        @test discount(fwd, 0.5) ≈ discount(curve, 1, 1.5)
        @test discount(fwd, 1) ≈ discount(curve, 1, 2)
        @test accumulation(fwd, 1) ≈ accumulation(curve, 1, 2)

        @test zero(fwd, 1) ≈ forward(curve, 1, 2)
    end


    @testset "actual cmt treasury" begin
        # Fabozzi 5-5,5-6
        cmt = [5.25, 5.5, 5.75, 6.0, 6.25, 6.5, 6.75, 6.8, 7.0, 7.1, 7.15, 7.2, 7.3, 7.35, 7.4, 7.5, 7.6, 7.6, 7.7, 7.8] ./ 100
        mats = collect(0.5:0.5:10.0)
        qs = CMTYield.(cmt, mats)
        target_raw = [5.25, 5.5, 5.76, 6.02, 6.28, 6.55, 6.82, 6.87, 7.09, 7.2, 7.26, 7.31, 7.43, 7.48, 7.54, 7.67, 7.8, 7.79, 7.93, 8.07] ./ 100
        targets = Periodic(2).(target_raw)
        targets[1:2] .= Periodic(1)(target_raw[1:2]) # 1 year is a no-coupon, BEY yield, the rest are semiannual BEY

        curves = [
            fit(Spline.Linear(), qs),
            fit(Spline.Quadratic(), qs),
            fit(Spline.Cubic(), qs),
        ]

        @testset "curve bootstrapping choices" for curve in curves
            @testset "Fabozzi bootstrapped rates" for (r, mat, target) in zip(cmt, mats, targets)
                @test zero(curve, mat) ≈ target atol = 0.0001
            end
        end

        # Hull, problem 4.34
        adj = ((1 + 0.051813 / 2)^2 - 1) * 100
        cmt = [4.0816, adj, 5.4986, 5.862] ./ 100
        mats = [0.5, 1.0, 1.5, 2.0]
        curve = fit(Spline.Linear(), CMTYield.(cmt, mats))
        targets = Continuous.([4.0405, 5.1293, 5.4429, 5.8085] ./ 100)
        @testset "Hull bootstrapped rates" for (r, mat, target) in zip(cmt, mats, targets)
            @test zero(curve, mat) ≈ target atol = 0.001
        end

        # test that showing the curve doesn't error
        @test length(repr(curve)) > 0

        #     # https://www.federalreserve.gov/pubs/feds/2006/200628/200628abs.html
        #     # 2020-04-02 data
        #     cmt = [0.0945,0.2053,0.4431,0.7139,0.9724,1.2002,1.3925,1.5512,1.6805,1.7853,1.8704,1.9399,1.9972,2.045,2.0855,2.1203,2.1509,2.1783,2.2031,2.2261,2.2477,2.2683,2.2881,2.3074,2.3262,2.3447,2.3629,2.3809,2.3987,2.4164] ./ 100
        #     mats = collect(1:30)
        #     curve = FinanceModels.USCMT(cmt,mats)
        #     target = [0.0945,0.2053,0.444,0.7172,0.9802,1.2142,1.4137,1.5797,1.7161,1.8275,1.9183,1.9928,2.0543,2.1056,2.1492,2.1868,2.2198,2.2495,2.2767,2.302,2.3261,2.3494,2.372,2.3944,2.4167,2.439,2.4614,2.4839,2.5067,2.5297] ./ 100

        #     @testset "FRB data" for (t,mat,target) in zip(1:length(mats),mats,target)
        #         @show mat
        #         if mat >= 1
        #             @test rate(zero(curve,mat, FinanceModels.Continuous())) ≈ target[mat] atol=0.001
        #         end
        #     end
    end

    @testset "OIS" begin
        targets = Continuous.([0.017987, 0.01995, 0.02188, 0.024693, 0.029994, 0.040401])
        ois = [1.8, 2.0, 2.2, 2.5, 3.0, 4.0] ./ 100
        mats = [1 / 12, 1 / 4, 1 / 2, 1, 2, 5]
        qs = OISYield.(ois, mats)

        curve = fit(Spline.Linear(), qs)
        @testset "bootstrapped rates" for (mat, target) in zip(mats, targets)
            @test zero(curve, mat) ≈ target atol = 0.001
        end
        curve = fit(Spline.Cubic(), qs)
        @testset "bootstrapped rates" for (mat, target) in zip(mats, targets)
            @test zero(curve, mat) ≈ target atol = 0.001
        end
    end

    @testset "par" begin
        @testset "first payment logic" begin
            ct = Bond.coupon_times
            @test ct(0.5, 1) ≈ 0.5:1:0.5
            @test ct(1.5, 1) ≈ 0.5:1:1.5
            @test ct(0.75, 1) ≈ 0.75:1:0.75
            @test ct(1, 1) ≈ 1:1:1
            @test ct(1, 2) ≈ 0.5:0.5:1.0
            @test ct(0.5, 2) ≈ 0.5:0.5:0.5
            @test ct(1.5, 2) ≈ 0.5:0.5:1.5
        end

        # https://quant.stackexchange.com/questions/57608/how-to-compute-par-yield-from-zero-rate-curve
        c = fit(Spline.Cubic(), ZCBYield.(Continuous.([0.02, 0.025, 0.03, 0.035]), 0.5:0.5:2))
        @test FinanceModels.par(c, 2) ≈ Periodic(0.03508591, 2) atol = 0.000001

        c = Yield.Constant(0.04)
        @testset "misc combinations" for t in 0.5:0.5:5
            @test FinanceModels.par(c, t; frequency = 1) ≈ FinanceModels.Periodic(0.04, 1)
            @test FinanceModels.par(c, t) ≈ FinanceModels.Periodic(0.04, 1)
            @test FinanceModels.par(c, t, frequency = 4) ≈ FinanceModels.Periodic(0.04, 1)
        end

        @test FinanceModels.par(c, 0.6) ≈ FinanceModels.Periodic(0.04, 1)

        @testset "round trip" begin
            maturity = collect(1:10)

            pars = [6.0, 8.0, 9.5, 10.5, 11.0, 11.25, 11.38, 11.44, 11.48, 11.5] ./ 100

            curve = fit(Spline.Cubic(), ParYield.(pars, maturity))

            for (p, m) in zip(pars, maturity)
                @test par(curve, m) ≈ Periodic(p, 2) atol = 0.001
            end
        end


    end

end
